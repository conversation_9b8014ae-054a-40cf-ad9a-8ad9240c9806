{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/LoginButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogIn, LogOut, User } from 'lucide-react'\n\nexport default function LoginButton() {\n  const { user, signInWithGoogle, signOut, loading } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSignIn = async () => {\n    setIsLoading(true)\n    try {\n      await signInWithGoogle()\n    } catch (error) {\n      console.error('Sign in error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse bg-gray-200 rounded-md h-10 w-24\"></div>\n    )\n  }\n\n  if (user) {\n    return null // Account button will be handled by AccountButton component\n  }\n\n  return (\n    <button\n      onClick={handleSignIn}\n      disabled={isLoading}\n      className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n    >\n      {isLoading ? (\n        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n      ) : (\n        <>\n          <LogIn className=\"w-4 h-4 mr-2\" />\n          Sign in with Google\n        </>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;;;;;IAEnB;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,4DAA4D;;IAC1E;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;kBAET,0BACC,6LAAC;YAAI,WAAU;;;;;iCAEf;;8BACE,6LAAC,2MAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;;AAM5C;GApDwB;;QAC+B,kIAAA,CAAA,UAAO;;;KADtC", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/auth/AccountButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { User, Settings, LogOut, ChevronDown } from 'lucide-react'\n\nexport default function AccountButton() {\n  const { user, signOut, loading } = useAuth()\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const router = useRouter()\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSignOut = async () => {\n    setIsLoading(true)\n    try {\n      await signOut()\n      setIsDropdownOpen(false)\n      router.push('/')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleProfileClick = () => {\n    setIsDropdownOpen(false)\n    router.push('/profile')\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  if (loading || !user) {\n    return null\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n      >\n        {user.avatar_url ? (\n          <img\n            src={user.avatar_url}\n            alt={user.display_name}\n            className=\"w-8 h-8 rounded-full object-cover\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n            {getInitials(user.display_name)}\n          </div>\n        )}\n        <span className=\"hidden sm:block\">{user.display_name}</span>\n        <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isDropdownOpen && (\n        <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\">\n          <div className=\"py-1\">\n            <div className=\"px-4 py-2 text-sm text-gray-500 border-b\">\n              <div className=\"font-medium text-gray-900\">{user.display_name}</div>\n              <div className=\"text-xs\">{user.email}</div>\n            </div>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              Profile\n            </button>\n            \n            <button\n              onClick={handleProfileClick}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Account Settings\n            </button>\n            \n            <div className=\"border-t\">\n              <button\n                onClick={handleSignOut}\n                disabled={isLoading}\n                className=\"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50\"\n              >\n                {isLoading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3\"></div>\n                ) : (\n                  <LogOut className=\"w-4 h-4 mr-3\" />\n                )}\n                {isLoading ? 'Signing out...' : 'Sign Out'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,kBAAkB;gBACpB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;kCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,MAAM;YACN,kBAAkB;YAClB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,IAAI,WAAW,CAAC,MAAM;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAC7B,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;oBAET,KAAK,UAAU,iBACd,6LAAC;wBACC,KAAK,KAAK,UAAU;wBACpB,KAAK,KAAK,YAAY;wBACtB,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,YAAY;;;;;;kCAGlC,6LAAC;wBAAK,WAAU;kCAAmB,KAAK,YAAY;;;;;;kCACpD,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6BAA6B,EAAE,iBAAiB,eAAe,IAAI;;;;;;;;;;;;YAG7F,gCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA6B,KAAK,YAAY;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;8CAAW,KAAK,KAAK;;;;;;;;;;;;sCAGtC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,0BACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAEnB,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GApHwB;;QACa,kIAAA,CAAA,UAAO;QAI3B,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 344, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginButton from '@/components/auth/LoginButton'\nimport AccountButton from '@/components/auth/AccountButton'\nimport { Crown, Home, User, Gamepad2 } from 'lucide-react'\n\nexport default function Navigation() {\n  const { user } = useAuth()\n\n  return (\n    <nav className=\"bg-white shadow-lg\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Crown className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"text-xl font-bold text-gray-900\">ChessHub</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Home className=\"w-4 h-4\" />\n              <span>Home</span>\n            </Link>\n\n            <Link\n              href=\"/demo\"\n              className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n            >\n              <Gamepad2 className=\"w-4 h-4\" />\n              <span>Demo</span>\n            </Link>\n\n            {user && (\n              <>\n                <Link\n                  href=\"/play\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <Gamepad2 className=\"w-4 h-4\" />\n                  <span>Play</span>\n                </Link>\n                <Link\n                  href=\"/profile\"\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Profile</span>\n                </Link>\n              </>\n            )}\n\n            <LoginButton />\n            <AccountButton />\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;4BAGP,sBACC;;kDACE,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;0CAKZ,6LAAC,4IAAA,CAAA,UAAW;;;;;0CACZ,6LAAC,8IAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAzDwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/hooks/useChessGame.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { Chess, Square } from 'chess.js'\n\nexport interface ChessGameState {\n  game: Chess\n  gameId?: string\n  playerColor: 'white' | 'black'\n  isPlayerTurn: boolean\n  gameStatus: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  moveHistory: string[]\n  resignedBy?: 'white' | 'black'\n}\n\nexport function useChessGame(initialFen?: string) {\n  const [gameState, setGameState] = useState<ChessGameState>(() => ({\n    game: new Chess(initialFen),\n    playerColor: 'white',\n    isPlayerTurn: true,\n    gameStatus: 'active', // Start as active for demo mode\n    moveHistory: []\n  }))\n\n  const [selectedSquare, setSelectedSquare] = useState<Square | null>(null)\n  const [possibleMoves, setPossibleMoves] = useState<Square[]>([])\n\n  // Get possible moves for a square\n  const getPossibleMoves = useCallback((square: Square): Square[] => {\n    const moves = gameState.game.moves({ square, verbose: true })\n    return moves.map(move => move.to as Square)\n  }, [gameState.game])\n\n  // Make a move\n  const makeMove = useCallback((from: Square, to: Square, promotion?: string) => {\n    try {\n      // Create a copy of the game to test the move\n      const gameCopy = new Chess(gameState.game.fen())\n      const move = gameCopy.move({\n        from,\n        to,\n        promotion: promotion || 'q' // Default to queen promotion\n      })\n\n      if (move) {\n        // Update state with the new game state\n        setGameState(prev => ({\n          ...prev,\n          game: gameCopy, // Use the updated game copy\n          moveHistory: [...prev.moveHistory, move.san]\n        }))\n\n        return { success: true, move }\n      } else {\n        return { success: false, error: 'Invalid move' }\n      }\n    } catch (error) {\n      return { success: false, error: 'Invalid move' }\n    }\n  }, [gameState.game])\n\n  // Handle square click\n  const onSquareClick = useCallback((square: Square) => {\n    // If no square is selected, select this square if it has a piece\n    if (!selectedSquare) {\n      const piece = gameState.game.get(square)\n      if (piece) {\n        setSelectedSquare(square)\n        setPossibleMoves(getPossibleMoves(square))\n      }\n      return\n    }\n\n    // If the same square is clicked, deselect\n    if (selectedSquare === square) {\n      setSelectedSquare(null)\n      setPossibleMoves([])\n      return\n    }\n\n    // Try to make a move\n    const moveResult = makeMove(selectedSquare, square)\n\n    // Clear selection regardless of move success\n    setSelectedSquare(null)\n    setPossibleMoves([])\n\n    return moveResult\n  }, [selectedSquare, gameState.game, getPossibleMoves, makeMove])\n\n  // Handle piece drop (drag and drop)\n  const onPieceDrop = useCallback((sourceSquare: Square, targetSquare: Square) => {\n    const result = makeMove(sourceSquare, targetSquare)\n    return result?.success || false\n  }, [makeMove])\n\n  // Reset game\n  const resetGame = useCallback(() => {\n    setGameState(prev => ({\n      ...prev,\n      game: new Chess(),\n      moveHistory: [],\n      gameStatus: 'active', // Set to active for demo mode\n      winner: undefined,\n      resignedBy: undefined,\n      isPlayerTurn: true\n    }))\n    setSelectedSquare(null)\n    setPossibleMoves([])\n  }, [])\n\n  // Resign game\n  const resignGame = useCallback(() => {\n    const currentPlayer = gameState.game.turn() === 'w' ? 'white' : 'black'\n    const winner = currentPlayer === 'white' ? 'black' : 'white'\n\n    setGameState(prev => ({\n      ...prev,\n      gameStatus: 'completed',\n      winner,\n      resignedBy: currentPlayer,\n      isPlayerTurn: false\n    }))\n\n    // Clear any selections\n    setSelectedSquare(null)\n    setPossibleMoves([])\n  }, [gameState.game])\n\n  // Update game from external state (for multiplayer)\n  const updateGameState = useCallback((newState: Partial<ChessGameState>) => {\n    setGameState(prev => {\n      const updatedState = {\n        ...prev,\n        ...newState\n      }\n\n      // If a new game instance is provided, use it directly\n      if (newState.game) {\n        updatedState.game = newState.game\n      }\n\n      return updatedState\n    })\n  }, [])\n\n  // Check game status\n  useEffect(() => {\n    const game = gameState.game\n    let status: ChessGameState['gameStatus'] = 'active'\n    let winner: ChessGameState['winner'] = undefined\n\n    if (game.isGameOver()) {\n      status = 'completed'\n      if (game.isCheckmate()) {\n        winner = game.turn() === 'w' ? 'black' : 'white'\n      } else if (game.isDraw()) {\n        winner = 'draw'\n      }\n    }\n\n    if (status !== gameState.gameStatus || winner !== gameState.winner) {\n      setGameState(prev => ({\n        ...prev,\n        gameStatus: status,\n        winner\n      }))\n    }\n  }, [gameState.game, gameState.gameStatus, gameState.winner])\n\n  return {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    makeMove,\n    resetGame,\n    resignGame,\n    updateGameState,\n    isCheck: gameState.game.isCheck(),\n    isCheckmate: gameState.game.isCheckmate(),\n    isDraw: gameState.game.isDraw(),\n    isGameOver: gameState.game.isGameOver() || gameState.gameStatus === 'completed',\n    currentFen: gameState.game.fen(),\n    pgn: gameState.game.pgn()\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAgBO,SAAS,aAAa,UAAmB;;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAkB,IAAM,CAAC;gBAChE,MAAM,IAAI,sJAAA,CAAA,QAAK,CAAC;gBAChB,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,aAAa,EAAE;YACjB,CAAC;;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,kCAAkC;IAClC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACpC,MAAM,QAAQ,UAAU,IAAI,CAAC,KAAK,CAAC;gBAAE;gBAAQ,SAAS;YAAK;YAC3D,OAAO,MAAM,GAAG;8DAAC,CAAA,OAAQ,KAAK,EAAE;;QAClC;qDAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,cAAc;IACd,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC,MAAc,IAAY;YACtD,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,WAAW,IAAI,sJAAA,CAAA,QAAK,CAAC,UAAU,IAAI,CAAC,GAAG;gBAC7C,MAAM,OAAO,SAAS,IAAI,CAAC;oBACzB;oBACA;oBACA,WAAW,aAAa,IAAI,6BAA6B;gBAC3D;gBAEA,IAAI,MAAM;oBACR,uCAAuC;oBACvC;8DAAa,CAAA,OAAQ,CAAC;gCACpB,GAAG,IAAI;gCACP,MAAM;gCACN,aAAa;uCAAI,KAAK,WAAW;oCAAE,KAAK,GAAG;iCAAC;4BAC9C,CAAC;;oBAED,OAAO;wBAAE,SAAS;wBAAM;oBAAK;gBAC/B,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAe;gBACjD;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAe;YACjD;QACF;6CAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,sBAAsB;IACtB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACjC,iEAAiE;YACjE,IAAI,CAAC,gBAAgB;gBACnB,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;gBACjC,IAAI,OAAO;oBACT,kBAAkB;oBAClB,iBAAiB,iBAAiB;gBACpC;gBACA;YACF;YAEA,0CAA0C;YAC1C,IAAI,mBAAmB,QAAQ;gBAC7B,kBAAkB;gBAClB,iBAAiB,EAAE;gBACnB;YACF;YAEA,qBAAqB;YACrB,MAAM,aAAa,SAAS,gBAAgB;YAE5C,6CAA6C;YAC7C,kBAAkB;YAClB,iBAAiB,EAAE;YAEnB,OAAO;QACT;kDAAG;QAAC;QAAgB,UAAU,IAAI;QAAE;QAAkB;KAAS;IAE/D,oCAAoC;IACpC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC,cAAsB;YACrD,MAAM,SAAS,SAAS,cAAc;YACtC,OAAO,QAAQ,WAAW;QAC5B;gDAAG;QAAC;KAAS;IAEb,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC5B;uDAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM,IAAI,sJAAA,CAAA,QAAK;wBACf,aAAa,EAAE;wBACf,YAAY;wBACZ,QAAQ;wBACR,YAAY;wBACZ,cAAc;oBAChB,CAAC;;YACD,kBAAkB;YAClB,iBAAiB,EAAE;QACrB;8CAAG,EAAE;IAEL,cAAc;IACd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,MAAM,gBAAgB,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;YAChE,MAAM,SAAS,kBAAkB,UAAU,UAAU;YAErD;wDAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,YAAY;wBACZ;wBACA,YAAY;wBACZ,cAAc;oBAChB,CAAC;;YAED,uBAAuB;YACvB,kBAAkB;YAClB,iBAAiB,EAAE;QACrB;+CAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,oDAAoD;IACpD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACnC;6DAAa,CAAA;oBACX,MAAM,eAAe;wBACnB,GAAG,IAAI;wBACP,GAAG,QAAQ;oBACb;oBAEA,sDAAsD;oBACtD,IAAI,SAAS,IAAI,EAAE;wBACjB,aAAa,IAAI,GAAG,SAAS,IAAI;oBACnC;oBAEA,OAAO;gBACT;;QACF;oDAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,OAAO,UAAU,IAAI;YAC3B,IAAI,SAAuC;YAC3C,IAAI,SAAmC;YAEvC,IAAI,KAAK,UAAU,IAAI;gBACrB,SAAS;gBACT,IAAI,KAAK,WAAW,IAAI;oBACtB,SAAS,KAAK,IAAI,OAAO,MAAM,UAAU;gBAC3C,OAAO,IAAI,KAAK,MAAM,IAAI;oBACxB,SAAS;gBACX;YACF;YAEA,IAAI,WAAW,UAAU,UAAU,IAAI,WAAW,UAAU,MAAM,EAAE;gBAClE;8CAAa,CAAA,OAAQ,CAAC;4BACpB,GAAG,IAAI;4BACP,YAAY;4BACZ;wBACF,CAAC;;YACH;QACF;iCAAG;QAAC,UAAU,IAAI;QAAE,UAAU,UAAU;QAAE,UAAU,MAAM;KAAC;IAE3D,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,UAAU,IAAI,CAAC,OAAO;QAC/B,aAAa,UAAU,IAAI,CAAC,WAAW;QACvC,QAAQ,UAAU,IAAI,CAAC,MAAM;QAC7B,YAAY,UAAU,IAAI,CAAC,UAAU,MAAM,UAAU,UAAU,KAAK;QACpE,YAAY,UAAU,IAAI,CAAC,GAAG;QAC9B,KAAK,UAAU,IAAI,CAAC,GAAG;IACzB;AACF;GA5KgB", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/components/chess/ChessBoard.tsx"], "sourcesContent": ["'use client'\n\nimport { Chessboard } from 'react-chessboard'\nimport { Square, Chess } from 'chess.js'\nimport { useChessGame } from '@/hooks/useChessGame'\nimport { Crown, RotateCcw, Flag } from 'lucide-react'\nimport { useMemo, useEffect } from 'react'\n\ninterface ChessBoardProps {\n  gameId?: string\n  playerColor?: 'white' | 'black'\n  isSpectator?: boolean\n  onMove?: (move: any) => void\n  initialFen?: string\n  currentFen?: string // Current FEN from database\n  moves?: string[] // Move history from database\n  isPlayerTurn?: boolean // Whether it's the current player's turn\n}\n\nexport default function ChessBoard({\n  playerColor = 'white',\n  isSpectator = false,\n  onMove,\n  initialFen,\n  currentFen: externalFen,\n  moves: externalMoves,\n  isPlayerTurn = true\n}: ChessBoardProps) {\n  const {\n    gameState,\n    selectedSquare,\n    possibleMoves,\n    onSquareClick,\n    onPieceDrop,\n    resetGame,\n    resignGame,\n    updateGameState,\n    isCheck,\n    isCheckmate,\n    isDraw,\n    isGameOver,\n    currentFen\n  } = useChessGame(initialFen)\n\n  // Debug board configuration\n  console.log('🎮 ChessBoard config:', {\n    playerColor,\n    isSpectator,\n    isPlayerTurn,\n    currentFen: currentFen.split(' ')[0] + '...',\n    gameStatus: gameState.gameStatus\n  })\n\n  // Sync external game state with local state\n  useEffect(() => {\n    if (externalFen && externalFen !== currentFen) {\n      console.log('Syncing external FEN:', externalFen)\n      try {\n        // Create a new Chess instance with the external FEN to validate it\n        const externalGame = new Chess(externalFen)\n        updateGameState({\n          game: externalGame,\n          moveHistory: externalMoves || []\n        })\n      } catch (error) {\n        console.error('Invalid FEN from external source:', externalFen, error)\n      }\n    }\n  }, [externalFen, currentFen, externalMoves, updateGameState])\n\n  // Custom square styles for highlighting\n  const customSquareStyles = useMemo(() => {\n    const styles: { [square: string]: React.CSSProperties } = {}\n\n    // Highlight selected square\n    if (selectedSquare) {\n      styles[selectedSquare] = {\n        backgroundColor: 'rgba(255, 255, 0, 0.4)'\n      }\n    }\n\n    // Highlight possible moves\n    possibleMoves.forEach(square => {\n      styles[square] = {\n        background: 'radial-gradient(circle, rgba(0,0,0,.1) 25%, transparent 25%)',\n        borderRadius: '50%'\n      }\n    })\n\n    // Highlight check\n    if (isCheck) {\n      const kingSquare = gameState.game.board().flat().find(\n        piece => piece && piece.type === 'k' && piece.color === gameState.game.turn()\n      )\n      if (kingSquare) {\n        // Find king position - this is a simplified approach\n        for (let rank = 0; rank < 8; rank++) {\n          for (let file = 0; file < 8; file++) {\n            const square = String.fromCharCode(97 + file) + (8 - rank) as Square\n            const piece = gameState.game.get(square)\n            if (piece && piece.type === 'k' && piece.color === gameState.game.turn()) {\n              styles[square] = {\n                backgroundColor: 'rgba(255, 0, 0, 0.4)'\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return styles\n  }, [selectedSquare, possibleMoves, isCheck, gameState.game])\n\n  const handlePieceDrop = ({ sourceSquare, targetSquare }: any) => {\n    console.log('🎯 handlePieceDrop called:', { sourceSquare, targetSquare })\n    console.log('🎮 Board state:', { isSpectator, isPlayerTurn })\n\n    if (isSpectator) {\n      console.log('❌ Cannot move: spectator mode')\n      return false\n    }\n\n    // TEMPORARILY DISABLE TURN VALIDATION FOR DEBUGGING\n    console.log('⚠️ TURN VALIDATION TEMPORARILY DISABLED FOR DEBUGGING')\n\n    console.log('🔄 Attempting move with chess.js...')\n    const result = onPieceDrop(sourceSquare as Square, targetSquare as Square)\n    console.log('🎯 Chess.js move result:', result)\n\n    if (result && onMove) {\n      console.log('✅ Move successful, calling onMove...')\n      // Get the last move from the game\n      const history = gameState.game.history({ verbose: true })\n      const lastMove = history[history.length - 1]\n      console.log('📝 Last move:', lastMove)\n      onMove(lastMove)\n    } else {\n      console.log('❌ Move failed or no onMove callback')\n    }\n\n    return result\n  }\n\n  const handleSquareClick = ({ square }: any) => {\n    console.log('🎯 handleSquareClick called:', square)\n    console.log('🎮 Click state:', { isSpectator })\n\n    if (!isSpectator) {\n      console.log('🔄 Calling onSquareClick...')\n      const result = onSquareClick(square as Square)\n      console.log('🎯 Square click result:', result)\n    } else {\n      console.log('❌ Cannot click: spectator mode')\n    }\n  }\n\n  const getGameStatusMessage = () => {\n    // Check for resignation first\n    if (gameState.resignedBy) {\n      const resignedPlayer = gameState.resignedBy === 'white' ? 'White' : 'Black'\n      const winner = gameState.resignedBy === 'white' ? 'Black' : 'White'\n      return `${resignedPlayer} resigned! ${winner} wins!`\n    }\n    if (isCheckmate) {\n      const winner = gameState.game.turn() === 'w' ? 'Black' : 'White'\n      return `Checkmate! ${winner} wins!`\n    }\n    if (isDraw) {\n      return 'Game ended in a draw'\n    }\n    if (isCheck) {\n      return 'Check!'\n    }\n    if (gameState.gameStatus === 'waiting') {\n      return 'Waiting for opponent...'\n    }\n    return `${gameState.game.turn() === 'w' ? 'White' : 'Black'} to move`\n  }\n\n  return (\n    <div className=\"flex flex-col items-center space-y-6 w-full max-w-lg mx-auto\">\n      {/* Game Status */}\n      <div className=\"bg-white rounded-xl shadow-lg p-5 w-full text-center border border-gray-200\">\n        <div className=\"flex items-center justify-center space-x-3 mb-3\">\n          <Crown className=\"h-6 w-6 text-yellow-500\" />\n          <span className=\"font-bold text-gray-800 text-lg\">\n            {getGameStatusMessage()}\n          </span>\n        </div>\n\n        {gameState.gameStatus === 'active' && (\n          <div className=\"text-sm text-gray-600 bg-gray-50 rounded-lg py-2 px-3\">\n            Current Turn: <span className=\"font-semibold\">{gameState.game.turn() === 'w' ? 'White' : 'Black'}</span>\n          </div>\n        )}\n      </div>\n\n      {/* Chess Board */}\n      <div className=\"relative w-full\">\n        <Chessboard\n          options={{\n            position: currentFen,\n            onPieceDrop: handlePieceDrop,\n            onSquareClick: !isSpectator ? handleSquareClick : undefined,\n            boardOrientation: playerColor,\n            squareStyles: customSquareStyles,\n            allowDrawingArrows: true,\n            allowDragging: !isSpectator, // Explicitly enable dragging\n            boardStyle: {\n              borderRadius: '12px',\n              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1)',\n              width: '100%',\n              maxWidth: '480px',\n              aspectRatio: '1'\n            }\n          }}\n        />\n        \n        {/* Game Over Overlay */}\n        {isGameOver && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center rounded-xl backdrop-blur-sm\">\n            <div className=\"bg-white p-8 rounded-xl text-center shadow-2xl border border-gray-200 max-w-sm mx-4\">\n              <div className=\"mb-4\">\n                <div className=\"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Crown className=\"h-8 w-8 text-yellow-600\" />\n                </div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Game Over</h3>\n                <p className=\"text-gray-600 text-lg\">{getGameStatusMessage()}</p>\n              </div>\n              {!isSpectator && (\n                <button\n                  onClick={resetGame}\n                  className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-semibold\"\n                >\n                  <RotateCcw className=\"h-5 w-5 mr-2\" />\n                  New Game\n                </button>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Game Controls */}\n      {!isSpectator && gameState.gameStatus === 'active' && (\n        <div className=\"flex flex-col sm:flex-row gap-3 w-full\">\n          <button\n            onClick={resetGame}\n            className=\"flex-1 inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-semibold rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-sm\"\n          >\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\n            Reset Game\n          </button>\n\n          <button\n            onClick={() => {\n              if (window.confirm('Are you sure you want to resign? This will end the game.')) {\n                resignGame()\n              }\n            }}\n            className=\"flex-1 inline-flex items-center justify-center px-4 py-3 border border-red-300 text-sm font-semibold rounded-lg text-red-700 bg-white hover:bg-red-50 transition-colors duration-200 shadow-sm\"\n          >\n            <Flag className=\"h-4 w-4 mr-2\" />\n            Resign\n          </button>\n        </div>\n      )}\n\n      {/* Move History */}\n      <div className=\"bg-white rounded-xl shadow-lg p-6 w-full border border-gray-200\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"bg-blue-100 text-blue-600 rounded-lg p-2 mr-3\">\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          </div>\n          <h3 className=\"text-xl font-bold text-gray-800\">Move History</h3>\n        </div>\n        <div className=\"max-h-40 overflow-y-auto\">\n          {gameState.moveHistory.length > 0 ? (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n              {gameState.moveHistory.map((move, index) => (\n                <div key={index} className=\"flex items-center p-2 rounded-lg bg-gray-50 text-gray-700 font-mono text-sm\">\n                  <span className=\"text-gray-500 mr-2 font-semibold\">\n                    {Math.floor(index / 2) + 1}.{index % 2 === 0 ? '' : '..'}\n                  </span>\n                  <span className=\"font-semibold\">{move}</span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 mb-2\">\n                <svg className=\"w-12 h-12 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <p className=\"text-gray-500 font-medium\">No moves yet</p>\n              <p className=\"text-gray-400 text-sm mt-1\">Make your first move to start!</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAmBe,SAAS,WAAW,EACjC,cAAc,OAAO,EACrB,cAAc,KAAK,EACnB,MAAM,EACN,UAAU,EACV,YAAY,WAAW,EACvB,OAAO,aAAa,EACpB,eAAe,IAAI,EACH;;IAChB,MAAM,EACJ,SAAS,EACT,cAAc,EACd,aAAa,EACb,aAAa,EACb,WAAW,EACX,SAAS,EACT,UAAU,EACV,eAAe,EACf,OAAO,EACP,WAAW,EACX,MAAM,EACN,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;IAEjB,4BAA4B;IAC5B,QAAQ,GAAG,CAAC,yBAAyB;QACnC;QACA;QACA;QACA,YAAY,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACvC,YAAY,UAAU,UAAU;IAClC;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,eAAe,gBAAgB,YAAY;gBAC7C,QAAQ,GAAG,CAAC,yBAAyB;gBACrC,IAAI;oBACF,mEAAmE;oBACnE,MAAM,eAAe,IAAI,sJAAA,CAAA,QAAK,CAAC;oBAC/B,gBAAgB;wBACd,MAAM;wBACN,aAAa,iBAAiB,EAAE;oBAClC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC,aAAa;gBAClE;YACF;QACF;+BAAG;QAAC;QAAa;QAAY;QAAe;KAAgB;IAE5D,wCAAwC;IACxC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACjC,MAAM,SAAoD,CAAC;YAE3D,4BAA4B;YAC5B,IAAI,gBAAgB;gBAClB,MAAM,CAAC,eAAe,GAAG;oBACvB,iBAAiB;gBACnB;YACF;YAEA,2BAA2B;YAC3B,cAAc,OAAO;0DAAC,CAAA;oBACpB,MAAM,CAAC,OAAO,GAAG;wBACf,YAAY;wBACZ,cAAc;oBAChB;gBACF;;YAEA,kBAAkB;YAClB,IAAI,SAAS;gBACX,MAAM,aAAa,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI;yEACnD,CAAA,QAAS,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI;;gBAE7E,IAAI,YAAY;oBACd,qDAAqD;oBACrD,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;wBACnC,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,OAAQ;4BACnC,MAAM,SAAS,OAAO,YAAY,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;4BACzD,MAAM,QAAQ,UAAU,IAAI,CAAC,GAAG,CAAC;4BACjC,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,KAAK,UAAU,IAAI,CAAC,IAAI,IAAI;gCACxE,MAAM,CAAC,OAAO,GAAG;oCACf,iBAAiB;gCACnB;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,OAAO;QACT;iDAAG;QAAC;QAAgB;QAAe;QAAS,UAAU,IAAI;KAAC;IAE3D,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE,YAAY,EAAO;QAC1D,QAAQ,GAAG,CAAC,8BAA8B;YAAE;YAAc;QAAa;QACvE,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAa;QAAa;QAE3D,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,oDAAoD;QACpD,QAAQ,GAAG,CAAC;QAEZ,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,YAAY,cAAwB;QACnD,QAAQ,GAAG,CAAC,4BAA4B;QAExC,IAAI,UAAU,QAAQ;YACpB,QAAQ,GAAG,CAAC;YACZ,kCAAkC;YAClC,MAAM,UAAU,UAAU,IAAI,CAAC,OAAO,CAAC;gBAAE,SAAS;YAAK;YACvD,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC5C,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,OAAO;QACT,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC,EAAE,MAAM,EAAO;QACxC,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,QAAQ,GAAG,CAAC,mBAAmB;YAAE;QAAY;QAE7C,IAAI,CAAC,aAAa;YAChB,QAAQ,GAAG,CAAC;YACZ,MAAM,SAAS,cAAc;YAC7B,QAAQ,GAAG,CAAC,2BAA2B;QACzC,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,8BAA8B;QAC9B,IAAI,UAAU,UAAU,EAAE;YACxB,MAAM,iBAAiB,UAAU,UAAU,KAAK,UAAU,UAAU;YACpE,MAAM,SAAS,UAAU,UAAU,KAAK,UAAU,UAAU;YAC5D,OAAO,GAAG,eAAe,WAAW,EAAE,OAAO,MAAM,CAAC;QACtD;QACA,IAAI,aAAa;YACf,MAAM,SAAS,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;YACzD,OAAO,CAAC,WAAW,EAAE,OAAO,MAAM,CAAC;QACrC;QACA,IAAI,QAAQ;YACV,OAAO;QACT;QACA,IAAI,SAAS;YACX,OAAO;QACT;QACA,IAAI,UAAU,UAAU,KAAK,WAAW;YACtC,OAAO;QACT;QACA,OAAO,GAAG,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU,QAAQ,QAAQ,CAAC;IACvE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;oBAIJ,UAAU,UAAU,KAAK,0BACxB,6LAAC;wBAAI,WAAU;;4BAAwD;0CACvD,6LAAC;gCAAK,WAAU;0CAAiB,UAAU,IAAI,CAAC,IAAI,OAAO,MAAM,UAAU;;;;;;;;;;;;;;;;;;0BAM/F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8JAAA,CAAA,aAAU;wBACT,SAAS;4BACP,UAAU;4BACV,aAAa;4BACb,eAAe,CAAC,cAAc,oBAAoB;4BAClD,kBAAkB;4BAClB,cAAc;4BACd,oBAAoB;4BACpB,eAAe,CAAC;4BAChB,YAAY;gCACV,cAAc;gCACd,WAAW;gCACX,OAAO;gCACP,UAAU;gCACV,aAAa;4BACf;wBACF;;;;;;oBAID,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;gCAEvC,CAAC,6BACA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAUjD,CAAC,eAAe,UAAU,UAAU,KAAK,0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,6LAAC;wBACC,SAAS;4BACP,IAAI,OAAO,OAAO,CAAC,6DAA6D;gCAC9E;4BACF;wBACF;wBACA,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAe,SAAQ;8CACnD,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA+L,UAAS;;;;;;;;;;;;;;;;0CAGvO,6LAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;;kCAElD,6LAAC;wBAAI,WAAU;kCACZ,UAAU,WAAW,CAAC,MAAM,GAAG,kBAC9B,6LAAC;4BAAI,WAAU;sCACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,QAAQ,KAAK;gDAAE;gDAAE,QAAQ,MAAM,IAAI,KAAK;;;;;;;sDAEtD,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;;mCAJzB;;;;;;;;;iDASd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAoB,MAAK;wCAAe,SAAQ;kDAC7D,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAA0N,UAAS;;;;;;;;;;;;;;;;8CAGlQ,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;GA9RwB;;QAuBlB,+HAAA,CAAA,eAAY;;;KAvBM", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/GitHub/chess_web/src/app/game/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useParams } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { supabase } from '@/lib/supabase'\nimport Navigation from '@/components/layout/Navigation'\nimport ChessBoard from '@/components/chess/ChessBoard'\nimport { ArrowLeft, Users, MessageCircle } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface GameData {\n  id: string\n  white_player_id: string\n  black_player_id?: string\n  game_state: string\n  moves: string[]\n  status: 'waiting' | 'active' | 'completed' | 'abandoned'\n  winner?: 'white' | 'black' | 'draw'\n  white_player: {\n    display_name: string\n    avatar_url?: string\n  }\n  black_player?: {\n    display_name: string\n    avatar_url?: string\n  }\n}\n\nexport default function GamePage() {\n  const params = useParams()\n  const gameId = params.id as string\n  const { user } = useAuth()\n  \n  const [game, setGame] = useState<GameData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    if (gameId && user) {\n      fetchGame()\n      subscribeToGameUpdates()\n    }\n  }, [gameId, user])\n\n  const fetchGame = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('games')\n        .select(`\n          *,\n          white_player:users!games_white_player_id_fkey(display_name, avatar_url),\n          black_player:users!games_black_player_id_fkey(display_name, avatar_url)\n        `)\n        .eq('id', gameId)\n        .single()\n\n      if (error) throw error\n      \n      // Check if user is authorized to view this game\n      if (data.white_player_id !== user?.id && data.black_player_id !== user?.id) {\n        setError('You are not authorized to view this game')\n        return\n      }\n\n      setGame(data)\n    } catch (error) {\n      console.error('Error fetching game:', error)\n      setError('Game not found')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const subscribeToGameUpdates = () => {\n    const subscription = supabase\n      .channel(`game-${gameId}`)\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'games',\n          filter: `id=eq.${gameId}`\n        },\n        (payload) => {\n          console.log('Game update:', payload)\n          // Only refetch if this update wasn't made by the current user\n          // This prevents unnecessary refetches when the user makes their own move\n          if (payload.new && payload.old) {\n            const newMovesLength = payload.new.moves?.length || 0\n            const oldMovesLength = payload.old.moves?.length || 0\n\n            // If moves were added, this is a move update\n            if (newMovesLength > oldMovesLength) {\n              console.log('Move detected, refetching game state...')\n              fetchGame()\n            } else {\n              // Other updates (status changes, player joins, etc.)\n              fetchGame()\n            }\n          } else {\n            // New game or other changes\n            fetchGame()\n          }\n        }\n      )\n      .subscribe()\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }\n\n  const handleMove = async (move: any) => {\n    console.log('🎯 handleMove called with:', move)\n\n    if (!game || !user) {\n      console.log('❌ Missing game or user:', { game: !!game, user: !!user })\n      return\n    }\n\n    console.log('🎮 Game state:', {\n      status: game.status,\n      white_player: game.white_player_id,\n      black_player: game.black_player_id,\n      moves_count: game.moves.length,\n      current_turn: game.game_state.split(' ')[1] === 'w' ? 'White' : 'Black'\n    })\n\n    console.log('👤 User info:', {\n      id: user.id,\n      is_white: game.white_player_id === user.id,\n      is_black: game.black_player_id === user.id\n    })\n\n    // TEMPORARILY DISABLE ALL VALIDATION FOR DEBUGGING\n    console.log('⚠️ VALIDATION TEMPORARILY DISABLED FOR DEBUGGING')\n\n    try {\n      console.log('🔄 Attempting to update game...')\n\n      // Update game state in database\n      const newMoves = [...game.moves, move.san]\n      const { error } = await supabase\n        .from('games')\n        .update({\n          game_state: move.after, // FEN after the move\n          moves: newMoves,\n          updated_at: new Date().toISOString()\n        })\n        .eq('id', gameId)\n\n      if (error) {\n        console.error('❌ Game update error:', error)\n        throw error\n      }\n\n      console.log('✅ Game updated successfully')\n\n      // Also record the move in game_moves table\n      console.log('🔄 Attempting to record move...')\n      const { error: moveError } = await supabase\n        .from('game_moves')\n        .insert({\n          game_id: gameId,\n          player_id: user.id,\n          move: move.san,\n          fen_after: move.after,\n          move_number: newMoves.length\n        })\n\n      if (moveError) {\n        console.error('❌ Move recording error:', moveError)\n      } else {\n        console.log('✅ Move recorded successfully')\n      }\n\n    } catch (error) {\n      console.error('❌ Error in handleMove:', error)\n      alert(`Failed to make move: ${error.message}`)\n    }\n  }\n\n  const getPlayerColor = (): 'white' | 'black' => {\n    if (!game || !user) return 'white'\n    return game.white_player_id === user.id ? 'white' : 'black'\n  }\n\n  const isPlayerTurn = (): boolean => {\n    if (!game || !user) return false\n    \n    // Parse FEN to get current turn\n    const fenParts = game.game_state.split(' ')\n    const currentTurn = fenParts[1] // 'w' for white, 'b' for black\n    \n    const playerColor = getPlayerColor()\n    return (currentTurn === 'w' && playerColor === 'white') || \n           (currentTurn === 'b' && playerColor === 'black')\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">Loading game...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (error || !game) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation />\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              {error || 'Game not found'}\n            </h1>\n            <Link\n              href=\"/play\"\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Games\n            </Link>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation />\n      \n      <div className=\"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Link\n                href=\"/play\"\n                className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-2\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-1\" />\n                Back to Games\n              </Link>\n              <h1 className=\"text-3xl font-extrabold text-gray-900\">\n                Chess Game\n              </h1>\n            </div>\n            \n            <div className=\"text-right\">\n              <div className=\"text-sm text-gray-500\">Game Status</div>\n              <div className={`text-lg font-semibold capitalize ${\n                game.status === 'active' ? 'text-green-600' :\n                game.status === 'waiting' ? 'text-yellow-600' :\n                'text-gray-600'\n              }`}>\n                {game.status}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n          {/* Players Info */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            {/* White Player */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-3\">\n                {game.white_player.avatar_url ? (\n                  <img\n                    src={game.white_player.avatar_url}\n                    alt={game.white_player.display_name}\n                    className=\"w-10 h-10 rounded-full\"\n                  />\n                ) : (\n                  <div className=\"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <Users className=\"w-5 h-5 text-gray-600\" />\n                  </div>\n                )}\n                <div>\n                  <div className=\"font-medium text-gray-900\">\n                    {game.white_player.display_name}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">White</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Black Player */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-3\">\n                {game.black_player?.avatar_url ? (\n                  <img\n                    src={game.black_player.avatar_url}\n                    alt={game.black_player.display_name}\n                    className=\"w-10 h-10 rounded-full\"\n                  />\n                ) : (\n                  <div className=\"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center\">\n                    <Users className=\"w-5 h-5 text-gray-600\" />\n                  </div>\n                )}\n                <div>\n                  <div className=\"font-medium text-gray-900\">\n                    {game.black_player?.display_name || 'Waiting for player...'}\n                  </div>\n                  <div className=\"text-sm text-gray-500\">Black</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Chat placeholder */}\n            <div className=\"bg-white rounded-lg shadow-md p-4\">\n              <div className=\"flex items-center space-x-2 mb-3\">\n                <MessageCircle className=\"h-5 w-5 text-gray-400\" />\n                <span className=\"font-medium text-gray-900\">Chat</span>\n              </div>\n              <div className=\"text-sm text-gray-500 text-center py-4\">\n                Chat feature coming soon!\n              </div>\n            </div>\n          </div>\n\n          {/* Chess Board */}\n          <div className=\"lg:col-span-3 flex justify-center\">\n            <ChessBoard\n              gameId={gameId}\n              playerColor={getPlayerColor()}\n              isSpectator={game.status === 'completed' || game.status === 'abandoned'}\n              onMove={handleMove}\n              initialFen={game.game_state}\n              currentFen={game.game_state}\n              moves={game.moves}\n              isPlayerTurn={game.status === 'waiting' ? true : isPlayerTurn()}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AA6Be,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IACxB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,UAAU,MAAM;gBAClB;gBACA;YACF;QACF;6BAAG;QAAC;QAAQ;KAAK;IAEjB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;QAIT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,gDAAgD;YAChD,IAAI,KAAK,eAAe,KAAK,MAAM,MAAM,KAAK,eAAe,KAAK,MAAM,IAAI;gBAC1E,SAAS;gBACT;YACF;YAEA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,eAAe,yHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EACxB,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,MAAM,EAAE,QAAQ;QAC3B,GACA,CAAC;YACC,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,8DAA8D;YAC9D,yEAAyE;YACzE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,EAAE;gBAC9B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,KAAK,EAAE,UAAU;gBACpD,MAAM,iBAAiB,QAAQ,GAAG,CAAC,KAAK,EAAE,UAAU;gBAEpD,6CAA6C;gBAC7C,IAAI,iBAAiB,gBAAgB;oBACnC,QAAQ,GAAG,CAAC;oBACZ;gBACF,OAAO;oBACL,qDAAqD;oBACrD;gBACF;YACF,OAAO;gBACL,4BAA4B;gBAC5B;YACF;QACF,GAED,SAAS;QAEZ,OAAO;YACL,aAAa,WAAW;QAC1B;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,IAAI,CAAC,QAAQ,CAAC,MAAM;YAClB,QAAQ,GAAG,CAAC,2BAA2B;gBAAE,MAAM,CAAC,CAAC;gBAAM,MAAM,CAAC,CAAC;YAAK;YACpE;QACF;QAEA,QAAQ,GAAG,CAAC,kBAAkB;YAC5B,QAAQ,KAAK,MAAM;YACnB,cAAc,KAAK,eAAe;YAClC,cAAc,KAAK,eAAe;YAClC,aAAa,KAAK,KAAK,CAAC,MAAM;YAC9B,cAAc,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,MAAM,UAAU;QAClE;QAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC3B,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,eAAe,KAAK,KAAK,EAAE;YAC1C,UAAU,KAAK,eAAe,KAAK,KAAK,EAAE;QAC5C;QAEA,mDAAmD;QACnD,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,gCAAgC;YAChC,MAAM,WAAW;mBAAI,KAAK,KAAK;gBAAE,KAAK,GAAG;aAAC;YAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,YAAY,KAAK,KAAK;gBACtB,OAAO;gBACP,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM;YACR;YAEA,QAAQ,GAAG,CAAC;YAEZ,2CAA2C;YAC3C,QAAQ,GAAG,CAAC;YACZ,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxC,IAAI,CAAC,cACL,MAAM,CAAC;gBACN,SAAS;gBACT,WAAW,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG;gBACd,WAAW,KAAK,KAAK;gBACrB,aAAa,SAAS,MAAM;YAC9B;YAEF,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,qBAAqB,EAAE,MAAM,OAAO,EAAE;QAC/C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAC3B,OAAO,KAAK,eAAe,KAAK,KAAK,EAAE,GAAG,UAAU;IACtD;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;QAE3B,gCAAgC;QAChC,MAAM,WAAW,KAAK,UAAU,CAAC,KAAK,CAAC;QACvC,MAAM,cAAc,QAAQ,CAAC,EAAE,CAAC,+BAA+B;;QAE/D,MAAM,cAAc;QACpB,OAAO,AAAC,gBAAgB,OAAO,gBAAgB,WACvC,gBAAgB,OAAO,gBAAgB;IACjD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;8BACX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;8BACX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,SAAS;;;;;;0CAEZ,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;;8CAKxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;sDACvC,6LAAC;4CAAI,WAAW,CAAC,iCAAiC,EAChD,KAAK,MAAM,KAAK,WAAW,mBAC3B,KAAK,MAAM,KAAK,YAAY,oBAC5B,iBACA;sDACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMpB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,YAAY,CAAC,UAAU,iBAC3B,6LAAC;oDACC,KAAK,KAAK,YAAY,CAAC,UAAU;oDACjC,KAAK,KAAK,YAAY,CAAC,YAAY;oDACnC,WAAU;;;;;yEAGZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGrB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACZ,KAAK,YAAY,CAAC,YAAY;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,YAAY,EAAE,2BAClB,6LAAC;oDACC,KAAK,KAAK,YAAY,CAAC,UAAU;oDACjC,KAAK,KAAK,YAAY,CAAC,YAAY;oDACnC,WAAU;;;;;yEAGZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGrB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACZ,KAAK,YAAY,EAAE,gBAAgB;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;0CAO5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4IAAA,CAAA,UAAU;oCACT,QAAQ;oCACR,aAAa;oCACb,aAAa,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK;oCAC5D,QAAQ;oCACR,YAAY,KAAK,UAAU;oCAC3B,YAAY,KAAK,UAAU;oCAC3B,OAAO,KAAK,KAAK;oCACjB,cAAc,KAAK,MAAM,KAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D;GAhUwB;;QACP,qIAAA,CAAA,YAAS;QAEP,kIAAA,CAAA,UAAO;;;KAHF", "debugId": null}}]}