{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "cf15da6d796b922d0b1f9f7bccac76f1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bb3e88527b8ac357b3761ba8b5543acdf93698838f888dac8998e6a5b25a9d88", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5ad3e790962fdac15ca52f9552bdffc33575bdb2a3909fed1273e1c77352f999"}}}, "instrumentation": null, "functions": {}}